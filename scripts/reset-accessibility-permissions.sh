#!/bin/bash

# PasteBar - Reset macOS Accessibility Permissions Script
# This script helps reset accessibility permissions for PasteBar

echo "🔧 PasteBar Accessibility Permissions Reset Tool"
echo "================================================"
echo ""

# Get the bundle identifier from tauri.conf.json
BUNDLE_ID="app.anothervision.pasteBar"
echo "📱 Bundle ID: $BUNDLE_ID"
echo ""

# Check if PasteBar is running
if pgrep -f "PasteBar" > /dev/null; then
    echo "⚠️  PasteBar is currently running. Please quit the application first."
    echo "   You can quit PasteBar from the menu bar or use Activity Monitor."
    echo ""
    read -p "Press Enter after quitting PasteBar to continue..."
fi

echo "🧹 Clearing accessibility permissions cache..."

# Reset TCC (Transparency, Consent, and Control) database for accessibility
echo "   Resetting TCC database entries..."
sudo sqlite3 /Library/Application\ Support/com.apple.TCC/TCC.db "DELETE FROM access WHERE client='$BUNDLE_ID' AND service='kTCCServiceAccessibility';"

# Also reset user-level TCC database
if [ -f ~/Library/Application\ Support/com.apple.TCC/TCC.db ]; then
    echo "   Resetting user TCC database entries..."
    sqlite3 ~/Library/Application\ Support/com.apple.TCC/TCC.db "DELETE FROM access WHERE client='$BUNDLE_ID' AND service='kTCCServiceAccessibility';"
fi

# Clear accessibility cache
echo "   Clearing accessibility cache..."
sudo rm -rf /var/db/.AccessibilityAPIEnabled 2>/dev/null || true
sudo rm -rf /private/var/db/.AccessibilityAPIEnabled 2>/dev/null || true

# Reset accessibility daemon
echo "   Restarting accessibility daemon..."
sudo launchctl unload /System/Library/LaunchDaemons/com.apple.accessibility.AccessibilityInspector.plist 2>/dev/null || true
sudo launchctl load /System/Library/LaunchDaemons/com.apple.accessibility.AccessibilityInspector.plist 2>/dev/null || true

echo ""
echo "✅ Accessibility permissions have been reset!"
echo ""
echo "📋 Next steps:"
echo "   1. Launch PasteBar"
echo "   2. When prompted, grant accessibility permissions"
echo "   3. The app should now work properly"
echo ""
echo "💡 If you still experience issues:"
echo "   - Go to System Settings > Privacy & Security > Accessibility"
echo "   - Remove PasteBar from the list if it appears"
echo "   - Restart PasteBar and grant permissions when prompted"
echo ""
